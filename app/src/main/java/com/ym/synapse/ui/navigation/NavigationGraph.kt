package com.ym.synapse.ui.navigation

import android.net.Uri
import androidx.activity.result.ActivityResultRegistryOwner
import androidx.activity.compose.LocalActivityResultRegistryOwner
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.ym.synapse.HomeScreen
import com.ym.synapse.SettingsScreen
import kotlinx.coroutines.launch
import android.util.Log

/**
 * 导航路由定义
 */
sealed class Screen(val route: String, val label: String, val icon: ImageVector) {
    object Home : Screen("home", "主页", Icons.Filled.Home)
    object Settings : Screen("settings", "设置", Icons.Filled.Settings)
}

/**
 * 底部导航项目列表
 */
val bottomNavItems = listOf(
    Screen.Home,
    Screen.Settings
)

/**
 * 主应用导航组件
 */
@Composable
fun MainNavigationApp() {
    val navController = rememberNavController()
    
    Scaffold(
        bottomBar = {
            BottomNavigationBar(navController = navController)
        }
    ) { innerPadding ->
        NavigationGraph(
            navController = navController,
            modifier = Modifier.padding(innerPadding)
        )
    }
}

/**
 * 底部导航栏
 */
@Composable
fun BottomNavigationBar(navController: NavHostController) {
    NavigationBar {
        val navBackStackEntry by navController.currentBackStackEntryAsState()
        val currentDestination = navBackStackEntry?.destination
        
        bottomNavItems.forEach { screen ->
            NavigationBarItem(
                icon = { Icon(screen.icon, contentDescription = screen.label) },
                label = { Text(screen.label) },
                selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true,
                onClick = {
                    navController.navigate(screen.route) {
                        // 避免重复导航到同一个目的地
                        popUpTo(navController.graph.findStartDestination().id) {
                            saveState = true
                        }
                        launchSingleTop = true
                        restoreState = true
                    }
                }
            )
        }
    }
}

/**
 * 导航图
 */
@Composable
fun NavigationGraph(
    navController: NavHostController,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = navController,
        startDestination = Screen.Home.route,
        modifier = modifier
    ) {
        composable(Screen.Home.route) {
            HomeScreen()
        }

        composable(Screen.Settings.route) {
            SettingsScreen()
        }
    }
}

// OCR功能已删除，减小应用大小

/**
 * 导航扩展函数
 */
fun NavHostController.navigateToScreen(screen: Screen) {
    navigate(screen.route) {
        popUpTo(graph.findStartDestination().id) {
            saveState = true
        }
        launchSingleTop = true
        restoreState = true
    }
}

/**
 * 导航状态管理
 */
@Composable
fun rememberNavigationState(): NavigationState {
    val navController = rememberNavController()
    return remember(navController) {
        NavigationState(navController)
    }
}

/**
 * 导航状态类
 */
class NavigationState(
    val navController: NavHostController
) {
    fun navigateToHome() {
        navController.navigateToScreen(Screen.Home)
    }
    
    // OCR导航已删除
    
    fun navigateToSettings() {
        navController.navigateToScreen(Screen.Settings)
    }
    
    fun navigateBack() {
        navController.popBackStack()
    }
    
    val currentRoute: String?
        @Composable get() = navController.currentBackStackEntryAsState().value?.destination?.route
}
