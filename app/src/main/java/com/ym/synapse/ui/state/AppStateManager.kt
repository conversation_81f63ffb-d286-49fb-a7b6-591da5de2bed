package com.ym.synapse.ui.state

import android.content.Context
import androidx.compose.runtime.*
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.ym.synapse.data.repository.ConfigRepository
import com.ym.synapse.utils.PermissionHelper
import com.ym.synapse.utils.RootLspHelper
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine

/**
 * 应用状态管理器
 * 统一管理应用的全局状态
 */
class AppStateManager private constructor(
    private val context: Context,
    private val configRepository: ConfigRepository
) {
    
    companion object {
        @Volatile
        private var INSTANCE: AppStateManager? = null
        
        fun getInstance(context: Context): AppStateManager {
            return INSTANCE ?: synchronized(this) {
                val configRepo = ConfigRepository.getInstance(context)
                INSTANCE ?: AppStateManager(context.applicationContext, configRepo).also { INSTANCE = it }
            }
        }
    }
    
    // 应用状态流
    private val _appState = MutableStateFlow(AppState())
    val appState: StateFlow<AppState> = _appState.asStateFlow()
    
    // 权限状态流
    private val _permissionState = MutableStateFlow(PermissionState())
    val permissionState: StateFlow<PermissionState> = _permissionState.asStateFlow()
    
    // 初始化状态检查
    init {
        checkInitialState()
    }
    
    /**
     * 应用状态数据类
     */
    data class AppState(
        val isInitialized: Boolean = false,
        val currentScreen: AppScreen = AppScreen.Loading,
        val isLoading: Boolean = false,
        val error: String? = null,
        val showOnboarding: Boolean = false
    )
    
    /**
     * 权限状态数据类
     */
    data class PermissionState(
        val basicPermissions: List<PermissionHelper.PermissionInfo> = emptyList(),
        val allBasicPermissionsGranted: Boolean = false,
        val rootLspStatus: RootLspHelper.PermissionStatus? = null,
        val allPermissionsGranted: Boolean = false,
        val lastChecked: Long = 0L
    )
    
    /**
     * 应用屏幕枚举
     */
    enum class AppScreen {
        Loading,
        Onboarding,
        PermissionRequest,
        ApiConfig,
        Tutorial,
        Main
    }
    
    /**
     * 检查初始状态
     */
    private fun checkInitialState() {
        _appState.value = _appState.value.copy(isLoading = true)
        
        // 检查是否需要显示引导流程
        val shouldShowOnboarding = configRepository.shouldShowInitialSetup()
        
        _appState.value = _appState.value.copy(
            isLoading = false,
            isInitialized = true,
            showOnboarding = shouldShowOnboarding,
            currentScreen = if (shouldShowOnboarding) AppScreen.Onboarding else AppScreen.Main
        )
        
        // 检查权限状态
        refreshPermissions()
    }
    
    /**
     * 刷新权限状态
     */
    fun refreshPermissions() {
        val basicPermissions = PermissionHelper.getAllPermissions(context)
        val allBasicGranted = PermissionHelper.areAllRequiredPermissionsGranted(context)
        val rootLspStatus = RootLspHelper.checkAllPermissions(context)
        
        _permissionState.value = PermissionState(
            basicPermissions = basicPermissions,
            allBasicPermissionsGranted = allBasicGranted,
            rootLspStatus = rootLspStatus,
            allPermissionsGranted = allBasicGranted && rootLspStatus.hasRoot && 
                                 rootLspStatus.hasLspFramework && rootLspStatus.hasLspModule,
            lastChecked = System.currentTimeMillis()
        )
    }
    
    /**
     * 更新当前屏幕
     */
    fun updateCurrentScreen(screen: AppScreen) {
        _appState.value = _appState.value.copy(currentScreen = screen)
    }
    
    /**
     * 设置加载状态
     */
    fun setLoading(loading: Boolean) {
        _appState.value = _appState.value.copy(isLoading = loading)
    }
    
    /**
     * 设置错误信息
     */
    fun setError(error: String?) {
        _appState.value = _appState.value.copy(error = error)
    }
    
    /**
     * 完成引导流程
     */
    fun completeOnboarding() {
        configRepository.markSetupCompleted()
        _appState.value = _appState.value.copy(
            showOnboarding = false,
            currentScreen = AppScreen.Main
        )
    }
    
    /**
     * 重置应用状态
     */
    fun resetApp() {
        configRepository.resetAllConfigs()
        _appState.value = AppState(
            isInitialized = true,
            showOnboarding = true,
            currentScreen = AppScreen.Onboarding
        )
        refreshPermissions()
    }
    
    /**
     * 获取配置摘要
     */
    fun getConfigSummary(): String {
        return configRepository.getConfigSummary()
    }
}

/**
 * Compose中使用AppStateManager的Hook
 */
@Composable
fun rememberAppState(context: Context = androidx.compose.ui.platform.LocalContext.current): AppStateManager {
    return remember(context) {
        AppStateManager.getInstance(context)
    }
}

/**
 * 收集应用状态的Composable函数
 */
@Composable
fun collectAppState(appStateManager: AppStateManager): AppStateManager.AppState {
    return appStateManager.appState.collectAsStateWithLifecycle().value
}

/**
 * 收集权限状态的Composable函数
 */
@Composable
fun collectPermissionState(appStateManager: AppStateManager): AppStateManager.PermissionState {
    return appStateManager.permissionState.collectAsStateWithLifecycle().value
}

/**
 * 应用状态效果处理
 */
@Composable
fun AppStateEffect(
    appState: AppStateManager.AppState,
    onError: (String) -> Unit = {},
    onScreenChange: (AppStateManager.AppScreen) -> Unit = {}
) {
    // 处理错误
    appState.error?.let { error ->
        LaunchedEffect(error) {
            onError(error)
        }
    }
    
    // 处理屏幕变化
    LaunchedEffect(appState.currentScreen) {
        onScreenChange(appState.currentScreen)
    }
}
