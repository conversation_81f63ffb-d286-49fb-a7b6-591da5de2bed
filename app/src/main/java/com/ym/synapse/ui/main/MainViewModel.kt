package com.ym.synapse.ui.main

import android.content.Context
import androidx.lifecycle.viewModelScope
import com.ym.synapse.data.repository.ConfigRepository
import com.ym.synapse.ui.base.BaseViewModel
import com.ym.synapse.ui.base.UiEffect
import com.ym.synapse.ui.base.UiEvent
import com.ym.synapse.ui.base.UiState
import com.ym.synapse.utils.PermissionHelper
import com.ym.synapse.utils.RootLspHelper
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

/**
 * 主界面ViewModel
 * 管理应用的主要状态和导航逻辑
 */
class MainViewModel(
    private val configRepository: ConfigRepository
) : BaseViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    // UI效果（一次性事件）
    private val _uiEffect = MutableSharedFlow<MainUiEffect>()
    val uiEffect: SharedFlow<MainUiEffect> = _uiEffect.asSharedFlow()
    
    // 权限状态
    private val _permissionState = MutableStateFlow(PermissionState())
    val permissionState: StateFlow<PermissionState> = _permissionState.asStateFlow()
    
    init {
        // 监听配置变化
        viewModelScope.launch {
            combine(
                configRepository.apiConfig,
                configRepository.appState
            ) { apiConfig, appState ->
                _uiState.value = _uiState.value.copy(
                    apiConfig = apiConfig,
                    appState = appState,
                    shouldShowInitialSetup = configRepository.shouldShowInitialSetup()
                )
            }
        }
        
        // 初始化检查
        checkInitialState()
    }
    
    /**
     * 处理UI事件
     */
    fun handleEvent(event: MainUiEvent) {
        when (event) {
            is MainUiEvent.CheckPermissions -> checkPermissions(event.context)
            is MainUiEvent.NavigateToApiConfig -> navigateToApiConfig()
            is MainUiEvent.NavigateToTutorial -> navigateToTutorial()
            is MainUiEvent.CompleteSetup -> completeSetup()
            is MainUiEvent.ResetConfiguration -> resetConfiguration()
            is MainUiEvent.RefreshPermissions -> refreshPermissions(event.context)
        }
    }
    
    /**
     * 检查初始状态
     */
    private fun checkInitialState() {
        launchSafely(showLoading = false) {
            val shouldShowSetup = configRepository.shouldShowInitialSetup()
            _uiState.value = _uiState.value.copy(
                shouldShowInitialSetup = shouldShowSetup
            )
        }
    }
    
    /**
     * 检查权限
     */
    private fun checkPermissions(context: Context) {
        launchSafely(showLoading = false) {
            // 检查基本权限
            val basicPermissions = PermissionHelper.getAllPermissions(context)
            val allBasicGranted = PermissionHelper.areAllRequiredPermissionsGranted(context)
            
            // 检查Root和LSP权限
            val rootLspStatus = RootLspHelper.checkAllPermissions(context)
            
            _permissionState.value = PermissionState(
                basicPermissions = basicPermissions,
                allBasicPermissionsGranted = allBasicGranted,
                rootLspStatus = rootLspStatus,
                allPermissionsGranted = allBasicGranted && rootLspStatus.hasRoot && 
                                     rootLspStatus.hasLspFramework && rootLspStatus.hasLspModule
            )
        }
    }
    
    /**
     * 刷新权限状态
     */
    private fun refreshPermissions(context: Context) {
        checkPermissions(context)
        viewModelScope.launch {
            _uiEffect.emit(MainUiEffect.ShowMessage("权限状态已刷新"))
        }
    }
    
    /**
     * 导航到API配置
     */
    private fun navigateToApiConfig() {
        configRepository.updateShowApiConfig(true)
        configRepository.updateShowTutorial(false)
    }
    
    /**
     * 导航到教程
     */
    private fun navigateToTutorial() {
        configRepository.updateShowTutorial(true)
        configRepository.updateShowApiConfig(false)
    }
    
    /**
     * 完成设置
     */
    private fun completeSetup() {
        configRepository.markSetupCompleted()
        viewModelScope.launch {
            _uiEffect.emit(MainUiEffect.ShowMessage("设置完成！"))
        }
    }
    
    /**
     * 重置配置
     */
    private fun resetConfiguration() {
        launchSafely {
            configRepository.resetAllConfigs()
            _uiEffect.emit(MainUiEffect.ShowMessage("配置已重置"))
        }
    }
    
    /**
     * 获取配置摘要
     */
    fun getConfigSummary(): String {
        return configRepository.getConfigSummary()
    }
}

/**
 * 主界面UI状态
 */
data class MainUiState(
    val apiConfig: ConfigRepository.ApiConfig = ConfigRepository.ApiConfig(),
    val appState: ConfigRepository.AppState = ConfigRepository.AppState(),
    val shouldShowInitialSetup: Boolean = true,
    val currentScreen: MainScreen = MainScreen.Loading
)

/**
 * 权限状态
 */
data class PermissionState(
    val basicPermissions: List<PermissionHelper.PermissionInfo> = emptyList(),
    val allBasicPermissionsGranted: Boolean = false,
    val rootLspStatus: RootLspHelper.PermissionStatus? = null,
    val allPermissionsGranted: Boolean = false
)

/**
 * 主界面屏幕枚举
 */
enum class MainScreen {
    Loading,
    PermissionRequest,
    ApiConfig,
    Tutorial,
    Main
}

/**
 * 主界面UI事件
 */
sealed class MainUiEvent : UiEvent {
    data class CheckPermissions(val context: Context) : MainUiEvent()
    data class RefreshPermissions(val context: Context) : MainUiEvent()
    object NavigateToApiConfig : MainUiEvent()
    object NavigateToTutorial : MainUiEvent()
    object CompleteSetup : MainUiEvent()
    object ResetConfiguration : MainUiEvent()
}

/**
 * 主界面UI效果
 */
sealed class MainUiEffect : UiEffect {
    data class ShowMessage(val message: String) : MainUiEffect()
    data class NavigateToScreen(val screen: MainScreen) : MainUiEffect()
    object RequestPermissions : MainUiEffect()
}
