package com.ym.synapse

import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.ym.synapse.ui.main.MainViewModel
import com.ym.synapse.ui.main.MainUiEvent
import com.ym.synapse.ui.main.MainUiEffect
import com.ym.synapse.ui.main.MainScreen
import com.ym.synapse.ui.viewModel
import com.ym.synapse.ui.theme.SynapseTheme
import com.ym.synapse.utils.ResourceManager
import kotlinx.coroutines.flow.collectLatest

/**
 * 重构后的MainActivity
 * 使用MVVM架构，职责更加清晰
 */
class MainActivityNew : ComponentActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            SynapseTheme {
                MainApp()
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理资源
        ResourceManager.cleanupAllResources()
    }
}

@Composable
fun MainApp() {
    val context = LocalContext.current
    val appStateManager = com.ym.synapse.ui.state.rememberAppState(context)

    // 收集应用状态
    val appState = com.ym.synapse.ui.state.collectAppState(appStateManager)
    val permissionState = com.ym.synapse.ui.state.collectPermissionState(appStateManager)

    // 处理状态效果
    com.ym.synapse.ui.state.AppStateEffect(
        appState = appState,
        onError = { error ->
            Toast.makeText(context, error, Toast.LENGTH_LONG).show()
        }
    )

    // 根据状态显示不同的界面
    Box(modifier = Modifier.fillMaxSize()) {
        when (appState.currentScreen) {
            com.ym.synapse.ui.state.AppStateManager.AppScreen.Loading -> {
                LoadingScreen()
            }
            com.ym.synapse.ui.state.AppStateManager.AppScreen.Onboarding,
            com.ym.synapse.ui.state.AppStateManager.AppScreen.PermissionRequest,
            com.ym.synapse.ui.state.AppStateManager.AppScreen.ApiConfig,
            com.ym.synapse.ui.state.AppStateManager.AppScreen.Tutorial -> {
                InitialSetupFlow(
                    appState = appState,
                    permissionState = permissionState,
                    appStateManager = appStateManager
                )
            }
            com.ym.synapse.ui.state.AppStateManager.AppScreen.Main -> {
                MainAppContent()
            }
        }
    }
}

@Composable
fun LoadingScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "正在初始化...",
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@Composable
fun InitialSetupFlow(
    appState: com.ym.synapse.ui.state.AppStateManager.AppState,
    permissionState: com.ym.synapse.ui.state.AppStateManager.PermissionState,
    appStateManager: com.ym.synapse.ui.state.AppStateManager
) {
    val context = LocalContext.current

    when (appState.currentScreen) {
        com.ym.synapse.ui.state.AppStateManager.AppScreen.Onboarding -> {
            OnboardingScreen(
                onComplete = {
                    appStateManager.updateCurrentScreen(
                        com.ym.synapse.ui.state.AppStateManager.AppScreen.PermissionRequest
                    )
                }
            )
        }
        com.ym.synapse.ui.state.AppStateManager.AppScreen.PermissionRequest -> {
            if (!permissionState.allBasicPermissionsGranted) {
                com.ym.synapse.ui.permission.PermissionRequestScreen(
                    permissions = permissionState.basicPermissions,
                    onPermissionGranted = {
                        appStateManager.refreshPermissions()
                        appStateManager.updateCurrentScreen(
                            com.ym.synapse.ui.state.AppStateManager.AppScreen.ApiConfig
                        )
                    },
                    onApiConfigClick = {
                        appStateManager.updateCurrentScreen(
                            com.ym.synapse.ui.state.AppStateManager.AppScreen.ApiConfig
                        )
                    }
                )
            } else {
                // 权限已授予，跳转到API配置
                LaunchedEffect(Unit) {
                    appStateManager.updateCurrentScreen(
                        com.ym.synapse.ui.state.AppStateManager.AppScreen.ApiConfig
                    )
                }
            }
        }
        com.ym.synapse.ui.state.AppStateManager.AppScreen.ApiConfig -> {
            ApiConfigScreen(
                onBackClick = {
                    appStateManager.updateCurrentScreen(
                        com.ym.synapse.ui.state.AppStateManager.AppScreen.PermissionRequest
                    )
                },
                onConfigComplete = {
                    appStateManager.updateCurrentScreen(
                        com.ym.synapse.ui.state.AppStateManager.AppScreen.Tutorial
                    )
                }
            )
        }
        com.ym.synapse.ui.state.AppStateManager.AppScreen.Tutorial -> {
            TutorialScreen(
                onBackClick = {
                    appStateManager.updateCurrentScreen(
                        com.ym.synapse.ui.state.AppStateManager.AppScreen.ApiConfig
                    )
                },
                onComplete = {
                    appStateManager.completeOnboarding()
                }
            )
        }
        else -> {
            // 其他情况，显示加载或错误
            LoadingScreen()
        }
    }
}

@Composable
fun MainAppContent() {
    // 使用新的导航架构
    com.ym.synapse.ui.navigation.MainNavigationApp()
}

@Composable
fun OnboardingScreen(
    onComplete: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "欢迎使用 Synapse",
            style = MaterialTheme.typography.headlineLarge
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "智能截图分析助手",
            style = MaterialTheme.typography.bodyLarge
        )

        Spacer(modifier = Modifier.height(32.dp))

        Button(
            onClick = onComplete,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("开始设置")
        }
    }
}
