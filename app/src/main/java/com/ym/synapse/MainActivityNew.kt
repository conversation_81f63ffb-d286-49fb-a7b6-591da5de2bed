package com.ym.synapse

import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.ym.synapse.ui.main.MainViewModel
import com.ym.synapse.ui.main.MainUiEvent
import com.ym.synapse.ui.main.MainUiEffect
import com.ym.synapse.ui.main.MainScreen
import com.ym.synapse.ui.viewModel
import com.ym.synapse.ui.theme.SynapseTheme
import com.ym.synapse.utils.ResourceManager
import kotlinx.coroutines.flow.collectLatest

/**
 * 重构后的MainActivity
 * 使用MVVM架构，职责更加清晰
 */
class MainActivityNew : ComponentActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            SynapseTheme {
                MainApp()
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理资源
        ResourceManager.cleanupAllResources()
    }
}

@Composable
fun MainApp() {
    val context = LocalContext.current
    val viewModel: MainViewModel = viewModel(context)
    
    // 收集UI状态
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val permissionState by viewModel.permissionState.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    val error by viewModel.error.collectAsStateWithLifecycle()
    
    // 处理UI效果
    LaunchedEffect(viewModel) {
        viewModel.uiEffect.collectLatest { effect ->
            when (effect) {
                is MainUiEffect.ShowMessage -> {
                    Toast.makeText(context, effect.message, Toast.LENGTH_SHORT).show()
                }
                is MainUiEffect.NavigateToScreen -> {
                    // 处理导航逻辑
                }
                MainUiEffect.RequestPermissions -> {
                    // 处理权限请求
                }
            }
        }
    }
    
    // 初始化时检查权限
    LaunchedEffect(Unit) {
        viewModel.handleEvent(MainUiEvent.CheckPermissions(context))
    }
    
    // 显示错误信息
    error?.let { errorMessage ->
        LaunchedEffect(errorMessage) {
            Toast.makeText(context, errorMessage, Toast.LENGTH_LONG).show()
        }
    }
    
    // 根据状态显示不同的界面
    Box(modifier = Modifier.fillMaxSize()) {
        when {
            isLoading -> {
                LoadingScreen()
            }
            uiState.shouldShowInitialSetup -> {
                InitialSetupFlow(
                    uiState = uiState,
                    permissionState = permissionState,
                    onEvent = viewModel::handleEvent
                )
            }
            else -> {
                MainAppContent(
                    onEvent = viewModel::handleEvent
                )
            }
        }
    }
}

@Composable
fun LoadingScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "正在初始化...",
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@Composable
fun InitialSetupFlow(
    uiState: com.ym.synapse.ui.main.MainUiState,
    permissionState: com.ym.synapse.ui.main.PermissionState,
    onEvent: (MainUiEvent) -> Unit
) {
    val context = LocalContext.current
    
    when {
        !permissionState.allBasicPermissionsGranted -> {
            com.ym.synapse.ui.permission.PermissionRequestScreen(
                permissions = permissionState.basicPermissions,
                onPermissionGranted = {
                    onEvent(MainUiEvent.RefreshPermissions(context))
                },
                onApiConfigClick = {
                    onEvent(MainUiEvent.NavigateToApiConfig)
                }
            )
        }
        uiState.appState.showApiConfig || !uiState.apiConfig.isConfigured -> {
            ApiConfigScreen(
                onBackClick = {
                    // 处理返回逻辑
                },
                onConfigComplete = {
                    onEvent(MainUiEvent.NavigateToTutorial)
                }
            )
        }
        uiState.appState.showTutorial -> {
            TutorialScreen(
                onBackClick = {
                    // 处理返回逻辑
                },
                onComplete = {
                    onEvent(MainUiEvent.CompleteSetup)
                }
            )
        }
        else -> {
            // 设置完成，显示主界面
            MainAppContent(onEvent = onEvent)
        }
    }
}

@Composable
fun MainAppContent(
    onEvent: (MainUiEvent) -> Unit
) {
    // 这里放置主应用内容
    // 包括底部导航栏和各个页面
    MainNavigationContent()
}

@Composable
fun MainNavigationContent() {
    // 原来的导航内容
    // 这部分可以从原MainActivity中提取
    Text(
        text = "主应用内容",
        modifier = Modifier.fillMaxSize().wrapContentSize(Alignment.Center)
    )
}
