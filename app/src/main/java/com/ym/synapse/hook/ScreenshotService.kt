package com.ym.synapse.hook

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.FileObserver
import android.os.IBinder
import android.util.Log
import com.ym.synapse.R
import android.widget.Toast
import com.ym.synapse.utils.NotificationHelper
import com.ym.synapse.utils.RootUtils
// OCR相关导入已删除
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.io.File

class ScreenshotService : Service() {

        private var fileObserver: FileObserver? = null
    private val serviceJob = SupervisorJob()
    private val serviceScope = CoroutineScope(Dispatchers.IO + serviceJob)

    companion object {
        const val TAG = "ScreenshotService"
        private const val NOTIFICATION_CHANNEL_ID = "ScreenshotServiceChannel"
        private const val NOTIFICATION_ID = 1
        // Standard path for screenshots. This can be made configurable later.
        private val SCREENSHOTS_DIR = File(android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_PICTURES), "Screenshots")

        @Volatile
        private var isServiceRunning = false
    }

        override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Service created.")
        // Create the channel for our OCR notifications
        NotificationHelper.createNotificationChannel(this)
        startForegroundWithNotification()
        setupFileObserver()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 防止重复启动
        if (isServiceRunning) {
            Log.d(TAG, "Service already running, ignoring duplicate start request")
            return START_NOT_STICKY
        }

        isServiceRunning = true
        Log.d(TAG, "Service started. Starting to watch for screenshots.")
        fileObserver?.startWatching()
        Log.d(TAG, "FileObserver started for path: ${SCREENSHOTS_DIR.absolutePath}")

        // 使用协程替代Thread，避免阻塞和资源泄漏
        serviceScope.launch {
            try {
                // 使用协程延迟替代Thread.sleep
                kotlinx.coroutines.delay(500) // 500ms delay
                Log.d(TAG, "Showing silent notification and executing screenshot command.")

                // Show silent notification
                NotificationHelper.showSilentNotification(
                    applicationContext,
                    "截图中",
                    "正在捕获屏幕..."
                )

                // 尝试截图
                tryTakeScreenshot()
            } catch (e: Exception) {
                Log.e(TAG, "Screenshot process failed", e)
            }
        }

        return START_NOT_STICKY // We want the service to stop itself, not be restarted.
    }

    /**
     * 尝试截图，只使用最简单有效的方法
     * 修复：使用更新后的RootUtils.execute返回值
     */
    private fun tryTakeScreenshot() {
        Log.d(TAG, "Attempting to take screenshot...")

        try {
            // 只使用最常用的截图方法
            val success = RootUtils.execute("input keyevent 120")
            if (success) {
                Log.d(TAG, "Screenshot command executed successfully")
            } else {
                Log.w(TAG, "Screenshot command failed, trying alternative method")
                tryAlternativeScreenshot()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Screenshot failed: ${e.message}")
            // 如果失败，尝试备用方法
            tryAlternativeScreenshot()
        }
    }

    /**
     * 备用截图方法
     * 修复：使用更新后的RootUtils.execute返回值，添加更多备用方法
     */
    private fun tryAlternativeScreenshot() {
        Log.d(TAG, "Trying alternative screenshot methods...")

        val alternativeMethods = arrayOf(
            "input keyevent 26 && input keyevent 25", // 电源键+音量下键
            "screencap -p /sdcard/screenshot.png",     // 直接截图命令
            "input keyevent KEYCODE_SYSRQ"            // 系统请求键
        )

        for ((index, method) in alternativeMethods.withIndex()) {
            try {
                Log.d(TAG, "Trying alternative method ${index + 1}: $method")
                val success = RootUtils.execute(method)
                if (success) {
                    Log.d(TAG, "Alternative screenshot method ${index + 1} succeeded")
                    return
                } else {
                    Log.w(TAG, "Alternative screenshot method ${index + 1} failed")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Alternative screenshot method ${index + 1} failed: ${e.message}")
            }
        }

        Log.e(TAG, "All screenshot methods failed")
    }



    private fun setupFileObserver() {
        // Ensure the directory exists
        if (!SCREENSHOTS_DIR.exists()) {
            Log.w(TAG, "Screenshots directory does not exist, creating: ${SCREENSHOTS_DIR.absolutePath}")
            SCREENSHOTS_DIR.mkdirs()
        }

        // Listen for events that indicate a file has been completely written.
        val eventMask = FileObserver.CLOSE_WRITE or FileObserver.MOVED_TO
        fileObserver = object : FileObserver(SCREENSHOTS_DIR, eventMask) {
            override fun onEvent(event: Int, path: String?) {
                // We check for CLOSE_WRITE or MOVED_TO. The path should not be null.
                                // Ignore temporary files created by the system during the save process.
                // We only act on the final, renamed file.
                if (path != null && !path.startsWith(".pending-")) {
                    Log.i(TAG, "FileObserver event detected: ${eventToString(event)} on path: $path")
                    val filePath = File(SCREENSHOTS_DIR, path).absolutePath
                    Log.i(TAG, "Screenshot captured! Path: $filePath")

                    serviceScope.launch {
                        // 简化处理：只显示截图成功通知
                        launch(Dispatchers.Main) {
                            Toast.makeText(applicationContext, "截图已保存: $path", Toast.LENGTH_LONG).show()
                        }

                        // 显示简单的成功通知
                        NotificationHelper.showSilentNotification(
                            applicationContext,
                            "截图完成",
                            "截图已保存到: $path"
                        )

                        // 停止服务
                        stopSelf()
                    }
                }
            }
        }
    }

    private fun startForegroundWithNotification() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                "截图服务",
                NotificationManager.IMPORTANCE_DEFAULT
            )
            val manager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            manager.createNotificationChannel(channel)
        }

        val notification: Notification = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Notification.Builder(this, NOTIFICATION_CHANNEL_ID)
                .setContentTitle("Synapse 服务")
                .setContentText("等待截图...")
                .setSmallIcon(R.mipmap.ic_launcher) // Using the app icon for the notification
                .build()
        } else {
            @Suppress("DEPRECATION")
            Notification.Builder(this)
                .setContentTitle("Synapse 服务")
                .setContentText("等待截图...")
                .setSmallIcon(R.mipmap.ic_launcher) // Using the app icon for the notification
                .build()
        }
        startForeground(NOTIFICATION_ID, notification)
    }

    override fun onDestroy() {
        super.onDestroy()
        isServiceRunning = false

        // 安全地停止FileObserver
        try {
            fileObserver?.stopWatching()
            fileObserver = null
        } catch (e: Exception) {
            Log.w(TAG, "Error stopping FileObserver", e)
        }

        // 取消所有协程作业，防止内存泄漏
        try {
            serviceJob.cancel()
        } catch (e: Exception) {
            Log.w(TAG, "Error cancelling service scope", e)
        }

        Log.d(TAG, "Service destroyed, FileObserver stopped, and CoroutineScope cancelled.")
    }

        override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    private fun eventToString(event: Int): String {
        return when (event) {
            FileObserver.ACCESS -> "ACCESS"
            FileObserver.MODIFY -> "MODIFY"
            FileObserver.ATTRIB -> "ATTRIB"
            FileObserver.CLOSE_WRITE -> "CLOSE_WRITE"
            FileObserver.CLOSE_NOWRITE -> "CLOSE_NOWRITE"
            FileObserver.OPEN -> "OPEN"
            FileObserver.MOVED_FROM -> "MOVED_FROM"
            FileObserver.MOVED_TO -> "MOVED_TO"
            FileObserver.CREATE -> "CREATE"
            FileObserver.DELETE -> "DELETE"
            FileObserver.DELETE_SELF -> "DELETE_SELF"
            FileObserver.MOVE_SELF -> "MOVE_SELF"
            else -> "UNKNOWN_EVENT ($event)"
        }
    }
}

