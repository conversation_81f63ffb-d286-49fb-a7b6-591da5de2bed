package com.ym.synapse.hook

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.FileObserver
import android.os.IBinder
import android.util.Log
import com.ym.synapse.R
import com.ym.synapse.ResultDisplayActivity
import android.widget.Toast
import com.ym.synapse.utils.AiOcrHelper
import com.ym.synapse.utils.NotificationHelper
import com.ym.synapse.utils.RootUtils
import com.ym.synapse.data.AnalysisHistoryManager
import com.ym.synapse.data.AnalysisRecord
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.io.File

class ScreenshotService : Service() {

        private var fileObserver: FileObserver? = null
    private val serviceJob = SupervisorJob()
    private val serviceScope = CoroutineScope(Dispatchers.IO + serviceJob)

    companion object {
        const val TAG = "ScreenshotService"
        private const val NOTIFICATION_CHANNEL_ID = "ScreenshotServiceChannel"
        private const val NOTIFICATION_ID = 1
        // Standard path for screenshots. This can be made configurable later.
        private val SCREENSHOTS_DIR = File(android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_PICTURES), "Screenshots")

        @Volatile
        private var isServiceRunning = false
    }

        override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Service created.")
        // Create the channel for our OCR notifications
        NotificationHelper.createNotificationChannel(this)
        startForegroundWithNotification()
        setupFileObserver()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 防止重复启动
        if (isServiceRunning) {
            Log.d(TAG, "Service already running, ignoring duplicate start request")
            return START_NOT_STICKY
        }

        isServiceRunning = true
        Log.d(TAG, "Service started. Starting to watch for screenshots.")
        fileObserver?.startWatching()
        Log.d(TAG, "FileObserver started for path: ${SCREENSHOTS_DIR.absolutePath}")

        // Give the observer a moment to start, then take the screenshot.
        Thread {
            try {
                Thread.sleep(500) // 500ms delay
                Log.d(TAG, "Showing silent notification and executing screenshot command.")
                // Show silent notification
                NotificationHelper.showSilentNotification(
                    applicationContext,
                    "截图中",
                    "正在捕获屏幕..."
                )
                // 尝试截图
                tryTakeScreenshot()
            } catch (e: InterruptedException) {
                Log.e(TAG, "Thread sleep interrupted", e)
            }
        }.start()

        return START_NOT_STICKY // We want the service to stop itself, not be restarted.
    }

    /**
     * 尝试截图，只使用最简单有效的方法
     */
    private fun tryTakeScreenshot() {
        Log.d(TAG, "Attempting to take screenshot...")

        try {
            // 只使用最常用的截图方法
            RootUtils.execute("input keyevent 120")
            Log.d(TAG, "Screenshot command executed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Screenshot failed: ${e.message}")
            // 如果失败，尝试备用方法
            tryAlternativeScreenshot()
        }
    }

    /**
     * 备用截图方法
     */
    private fun tryAlternativeScreenshot() {
        Log.d(TAG, "Trying alternative screenshot method...")

        try {
            // 尝试组合键方法
            RootUtils.execute("input keyevent 26 && input keyevent 25")
            Log.d(TAG, "Alternative screenshot method executed")
        } catch (e: Exception) {
            Log.e(TAG, "Alternative screenshot failed: ${e.message}")
        }
    }



    private fun setupFileObserver() {
        // Ensure the directory exists
        if (!SCREENSHOTS_DIR.exists()) {
            Log.w(TAG, "Screenshots directory does not exist, creating: ${SCREENSHOTS_DIR.absolutePath}")
            SCREENSHOTS_DIR.mkdirs()
        }

        // Listen for events that indicate a file has been completely written.
        val eventMask = FileObserver.CLOSE_WRITE or FileObserver.MOVED_TO
        fileObserver = object : FileObserver(SCREENSHOTS_DIR, eventMask) {
            override fun onEvent(event: Int, path: String?) {
                // We check for CLOSE_WRITE or MOVED_TO. The path should not be null.
                                // Ignore temporary files created by the system during the save process.
                // We only act on the final, renamed file.
                if (path != null && !path.startsWith(".pending-")) {
                    Log.i(TAG, "FileObserver event detected: ${eventToString(event)} on path: $path")
                    val filePath = File(SCREENSHOTS_DIR, path).absolutePath
                    Log.i(TAG, "Screenshot captured! Path: $filePath")

                    serviceScope.launch {
                        // Show processing notification
                        NotificationHelper.showProcessingNotification(
                            applicationContext,
                            "分析中",
                            "正在识别图片类型..."
                        )

                        // Step 1: Perform classification
                        val category = AiOcrHelper.classifyImage(applicationContext, filePath)
                        Log.i(TAG, "Image classified as: $category")

                        // Show classification result
                        launch(Dispatchers.Main) {
                            Toast.makeText(applicationContext, "类别: $category", Toast.LENGTH_LONG).show()
                        }

                        // Step 2: Process image based on its type
                        NotificationHelper.showProcessingNotification(
                            applicationContext,
                            "处理中",
                            "正在使用 $category 模型处理..."
                        )

                        val result = AiOcrHelper.processImageByType(applicationContext, filePath, category)
                        Log.i(TAG, "Image processing result: $result")

                        // 保存分析记录到历史
                        val analysisRecord = AnalysisRecord(
                            imageType = category,
                            resultText = result,
                            imagePath = filePath
                        )
                        AnalysisHistoryManager.addRecord(applicationContext, analysisRecord)
                        Log.d(TAG, "Analysis record saved to history")

                        // Show final result
                        launch(Dispatchers.Main) {
                            Toast.makeText(applicationContext, "处理完成，点击通知查看详细结果", Toast.LENGTH_LONG).show()
                        }

                        // Create intent for result display
                        val resultIntent = ResultDisplayActivity.createIntent(
                            applicationContext,
                            category,
                            result,
                            filePath
                        )

                        // Show result notification with click action
                        NotificationHelper.showResultNotificationWithAction(
                            applicationContext,
                            "处理完成",
                            "类别: $category - 点击查看详细结果",
                            resultIntent
                        )

                        // Stop the service after processing is complete
                        stopSelf()
                    }
                }
            }
        }
    }

    private fun startForegroundWithNotification() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                "截图服务",
                NotificationManager.IMPORTANCE_DEFAULT
            )
            val manager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            manager.createNotificationChannel(channel)
        }

        val notification: Notification = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Notification.Builder(this, NOTIFICATION_CHANNEL_ID)
                .setContentTitle("Synapse 服务")
                .setContentText("等待截图...")
                .setSmallIcon(R.mipmap.ic_launcher) // Using the app icon for the notification
                .build()
        } else {
            @Suppress("DEPRECATION")
            Notification.Builder(this)
                .setContentTitle("Synapse 服务")
                .setContentText("等待截图...")
                .setSmallIcon(R.mipmap.ic_launcher) // Using the app icon for the notification
                .build()
        }
        startForeground(NOTIFICATION_ID, notification)
    }

        override fun onDestroy() {
        super.onDestroy()
        isServiceRunning = false
        fileObserver?.stopWatching()
        serviceJob.cancel() // Cancel all coroutines when the service is destroyed
        Log.d(TAG, "Service destroyed, FileObserver stopped, and CoroutineScope cancelled.")
    }

        override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    private fun eventToString(event: Int): String {
        return when (event) {
            FileObserver.ACCESS -> "ACCESS"
            FileObserver.MODIFY -> "MODIFY"
            FileObserver.ATTRIB -> "ATTRIB"
            FileObserver.CLOSE_WRITE -> "CLOSE_WRITE"
            FileObserver.CLOSE_NOWRITE -> "CLOSE_NOWRITE"
            FileObserver.OPEN -> "OPEN"
            FileObserver.MOVED_FROM -> "MOVED_FROM"
            FileObserver.MOVED_TO -> "MOVED_TO"
            FileObserver.CREATE -> "CREATE"
            FileObserver.DELETE -> "DELETE"
            FileObserver.DELETE_SELF -> "DELETE_SELF"
            FileObserver.MOVE_SELF -> "MOVE_SELF"
            else -> "UNKNOWN_EVENT ($event)"
        }
    }
}

