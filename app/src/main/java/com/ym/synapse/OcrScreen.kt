package com.ym.synapse

import android.content.Context
import android.graphics.Bitmap
import android.graphics.ImageDecoder
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.util.Log
import retrofit2.HttpException
import com.google.gson.Gson
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResultRegistryOwner
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import com.ym.synapse.KEY_AI_OCR_PROMPT
import com.ym.synapse.DEFAULT_AI_OCR_PROMPT
import com.ym.synapse.KEY_AI_OCR_MODEL_ID // This is correct
import com.ym.synapse.DEFAULT_AI_OCR_MODEL_ID
import com.ym.synapse.KEY_API_URL 
import com.ym.synapse.KEY_API_KEY 
import com.ym.synapse.PREFS_NAME 
import com.ym.synapse.R // Ensure R is imported for string resources
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import com.ym.synapse.KEY_API_KEY
import com.ym.synapse.KEY_AI_OCR_MODEL_ID
import com.ym.synapse.KEY_API_URL
import com.ym.synapse.PREFS_NAME
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.chinese.ChineseTextRecognizerOptions
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.IOException

// Imports for AI OCR
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Url
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import java.io.ByteArrayOutputStream
import java.util.Base64 // For API 26+ java.util.Base64

// Kotlin Coroutines imports
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.CoroutineScope

// --- AI OCR Network Related ---

// Data classes for OpenAI-compatible Chat Completions API with image input

data class ImageUrl(val url: String)

data class ContentPart(val type: String, val text: String? = null, val image_url: ImageUrl? = null)

data class Message(val role: String, val content: List<ContentPart>)

data class AiOcrRequest(
    val model: String, // Model identifier
    val messages: List<Message>,
    val max_tokens: Int = 1000 // Optional: control response length, adjust as needed
)

data class AiOcrChoice(
    val message: AiOcrResponseMessage?,
    val finish_reason: String? = null, // Optional: good for debugging
    val index: Int? = null             // Optional
)

data class AiOcrResponseMessage(
    val role: String?,
    val content: String?
    // val tool_calls: List<Any>? = null // Optional, if your models use tools
)

data class AiOcrError(
    val message: String?,
    val type: String? = null,
    val param: String? = null,
    val code: String? = null
)

data class AiOcrResponse(
    val id: String? = null,
    val choices: List<AiOcrChoice>? = null,
    // val usage: Any? = null, // Optional: token usage info
    // val model: String? = null, // Optional: model name used
    // val created: Long? = null, // Optional: timestamp
    // val `object`: String? = null, // Optional: e.g., "chat.completion"
    val error: AiOcrError? = null // For API-level errors returned in the JSON body
)

interface AiOcrApiService {
    @POST
    suspend fun recognizeImage(
        @Url fullUrl: String, // Use @Url for fully dynamic URLs. The path part of URL should be included here if not in base URL.
        @Header("Authorization") authorization: String,
        @Body request: AiOcrRequest
    ): AiOcrResponse
}

// Helper function to convert Bitmap to Base64 String
fun Bitmap.toBase64(): String {
    val outputStream = ByteArrayOutputStream()
    this.compress(Bitmap.CompressFormat.JPEG, 90, outputStream) // Changed to JPEG, quality 90
    val byteArray = outputStream.toByteArray()
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
        Base64.getEncoder().encodeToString(byteArray)
    } else {
        android.util.Base64.encodeToString(byteArray, android.util.Base64.NO_WRAP)
    }
}

// --- End of AI OCR Network Related ---

// OcrScreen Composable - Copied and adapted from OcrActivity.kt
@Composable
fun OcrScreen(ocrHandler: OcrHandler, onLaunchImagePicker: () -> Unit) {
    val resultText by ocrHandler.resultText.collectAsState()
    val selectedBitmap by ocrHandler.selectedImageBitmap.collectAsState()
    val context = LocalContext.current // Already used, good.

    Column(
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding() // Add padding for the status bar
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top
    ) {
        Text(stringResource(id = R.string.ocr_title), style = MaterialTheme.typography.headlineMedium)
        Spacer(modifier = Modifier.height(24.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Button(onClick = { 
                ocrHandler.selectImageForLocalOcr() // This prepares the state
                onLaunchImagePicker() // This actually launches the picker
            }) {
                Text(stringResource(id = R.string.ocr_button_local))
            }
            Button(onClick = {
                val currentSelectedUri = ocrHandler.selectedImageUri.value // Access the value of StateFlow
                val currentImageUri = ocrHandler.selectedImageUri.value
                if (currentImageUri != null) {
                    ocrHandler.performAiOcr(currentImageUri)
                } else {
                    onLaunchImagePicker()
                }
            }) {
                Text(stringResource(id = R.string.ocr_button_ai))
            }
        }

        selectedBitmap?.let { bitmap ->
            Spacer(modifier = Modifier.height(16.dp))
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 200.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)),
                contentAlignment = Alignment.Center
            ) {
                var scale by remember { mutableStateOf(1f) }
                var offsetX by remember { mutableStateOf(0f) }
                var offsetY by remember { mutableStateOf(0f) }
                Image(
                    bitmap = bitmap.asImageBitmap(),
                    contentDescription = stringResource(id = R.string.ocr_image_preview_desc),
                    modifier = Modifier
                        .fillMaxSize()
                        .pointerInput(Unit) {
                            detectTransformGestures {
                                centroid, pan, zoom, _ ->
                                val oldScale = scale
                                scale = (scale * zoom).coerceIn(1f, 3f)

                                if (oldScale != 0f && scale != oldScale) {
                                     offsetX += (centroid.x - size.width / 2) - ( (centroid.x - size.width / 2) / oldScale * scale )
                                     offsetY += (centroid.y - size.height / 2) - ( (centroid.y - size.height / 2) / oldScale * scale )
                                }

                                offsetX += pan.x
                                offsetY += pan.y

                                val maxOffsetX = (size.width * (scale - 1)) / 2
                                val maxOffsetY = (size.height * (scale - 1)) / 2
                                offsetX = offsetX.coerceIn(-maxOffsetX, maxOffsetX)
                                offsetY = offsetY.coerceIn(-maxOffsetY, maxOffsetY)

                                if (scale == 1f) {
                                    offsetX = 0f
                                    offsetY = 0f
                                }
                            }
                        }
                        .graphicsLayer(
                            scaleX = scale,
                            scaleY = scale,
                            translationX = offsetX,
                            translationY = offsetY
                        )
                        .padding(4.dp),
                    contentScale = ContentScale.Fit
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
        }

        if (selectedBitmap == null) {
            Spacer(modifier = Modifier.height(32.dp))
        }

        Surface(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = 8.dp),
            tonalElevation = 2.dp,
            shape = RoundedCornerShape(8.dp),
            border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.5f))
        ) {
            Text(
                text = resultText,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState()),
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

// OcrHandler class - Copied and adapted from OcrActivity.kt
class OcrHandler(
    private val context: Context, // Changed from Activity to Context
    activityResultRegistryOwner: ActivityResultRegistryOwner, // Added for registerForActivityResult
    private val onResult: (String) -> Unit,
    private val coroutineScope: CoroutineScope // Added CoroutineScope
) {

    private val _selectedImageBitmap = MutableStateFlow<Bitmap?>(null)
    val selectedImageBitmap = _selectedImageBitmap.asStateFlow()

    private val _selectedImageUri = MutableStateFlow<Uri?>(null)
    val selectedImageUri = _selectedImageUri.asStateFlow()
    // context is already a constructor parameter

    private val _resultText = MutableStateFlow(context.getString(R.string.ocr_results_placeholder))
    val resultText = _resultText.asStateFlow()

    // IMPORTANT: registerForActivityResult must be called from a Composable or an Activity/Fragment.
    // We will call it from the Composable that uses OcrHandler.
    // This launcher will be initialized by the Composable that creates OcrHandler.
    // For now, we declare it, and it will be assigned in the Composable.
    // This is a common pattern when abstracting ActivityResultLauncher usage.
    // However, a cleaner way is to pass the launcher itself to the OcrHandler.
    // Let's try passing the launcher directly.

    // This is the old way, we will change this:
    // private val selectImageLauncher = 
    //     activity.registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? -> ... }
    // Instead, the launcher will be passed in or created differently.
    // For now, we'll make selectImageForLocalOcr take the launcher as a parameter or assume it's set.
    // A better approach for Compose is to have the Composable own the launcher.

    // Let's adjust selectImageForLocalOcr to be called by the Composable which owns the launcher.
    // The Composable will call launcher.launch("image/*")
    // And the result callback of the launcher will call a method on OcrHandler.

    // New approach: The Composable will own the launcher and call processUri on result.
    fun processUri(uri: Uri?) {
        uri?.let {
            _selectedImageUri.value = it // Store the URI
            _selectedImageBitmap.value = null
            _resultText.value = context.getString(R.string.ocr_image_selected_processing)
            try {
                val bitmap = loadBitmapFromUri(it)
                _selectedImageBitmap.value = bitmap
                processImageWithMlKit(bitmap)
            } catch (e: IOException) {
                Log.e("OcrHandler", "Failed to load bitmap", e)
                _resultText.value = context.getString(R.string.ocr_load_image_failed)
                _selectedImageBitmap.value = null
            }
        } ?: run {
            _resultText.value = context.getString(R.string.ocr_no_image_selected)
            _selectedImageBitmap.value = null
        }
    }

    // This method will now be called by the Composable after the launcher is invoked.
    fun prepareForImageSelection() {
        _resultText.value = context.getString(R.string.ocr_select_image_prompt)
        // The actual launcher.launch("image/*") will be done in the Composable
    }
    
    // Renamed from selectImageForLocalOcr to reflect its new role
    // This is effectively a signal that the Composable should launch the picker.
    // The actual launch and result handling will be in the Composable.
    // So, the Composable will call: 
    // 1. ocrHandler.prepareForImageSelection()
    // 2. imagePickerLauncher.launch("image/*")
    // Then, in the launcher's callback: ocrHandler.processUri(uri)
    fun selectImageForLocalOcr() { // This name is fine, it signals intent from UI
        prepareForImageSelection() 
        // The Composable that calls this will then launch its own ActivityResultLauncher
    }


    private fun loadBitmapFromUri(uri: Uri): Bitmap {
        return if (Build.VERSION.SDK_INT < 28) {
            @Suppress("DEPRECATION")
            MediaStore.Images.Media.getBitmap(context.contentResolver, uri) // Changed activity.contentResolver to context.contentResolver
        } else {
            val source = ImageDecoder.createSource(context.contentResolver, uri) // Changed activity.contentResolver to context.contentResolver
            ImageDecoder.decodeBitmap(source)
        }.copy(Bitmap.Config.ARGB_8888, true)
    }

    fun clearPreviewAndResult() {
        _selectedImageBitmap.value = null
        _selectedImageUri.value = null // Clear the stored URI
        _resultText.value = context.getString(R.string.ocr_results_placeholder)
    }

    private fun processImageWithMlKit(bitmap: Bitmap) {
        val image = InputImage.fromBitmap(bitmap, 0)
        val recognizer = TextRecognition.getClient(ChineseTextRecognizerOptions.Builder().build())

        recognizer.process(image)
            .addOnSuccessListener { visionText ->
                val result = visionText.text.ifEmpty { context.getString(R.string.ocr_no_text_found) }
                _resultText.value = result
                onResult(result)
            }
            .addOnFailureListener { e ->
                Log.e("OcrHandler", "Text recognition failed", e)
                val errorMsg = context.getString(R.string.ocr_recognition_failed, e.message ?: "Unknown error")
                _resultText.value = errorMsg
                onResult(errorMsg)
            }
            .addOnCompleteListener {
                // 确保recognizer资源被释放
                try {
                    recognizer.close()
                } catch (e: Exception) {
                    Log.w("OcrHandler", "Error closing recognizer", e)
                }
            }
    }

    fun performAiOcr(imageUri: Uri) {
        // 1. Read settings from SharedPreferences
        val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val apiUrl = sharedPreferences.getString(KEY_API_URL, null)
        val apiKey = sharedPreferences.getString(KEY_API_KEY, null)
        val modelId = sharedPreferences.getString(KEY_AI_OCR_MODEL_ID, DEFAULT_AI_OCR_MODEL_ID) ?: DEFAULT_AI_OCR_MODEL_ID
        val userPrompt = sharedPreferences.getString(KEY_AI_OCR_PROMPT, DEFAULT_AI_OCR_PROMPT) ?: DEFAULT_AI_OCR_PROMPT
        Log.d("OcrHandler", "Read from SharedPreferences - API URL: $apiUrl, API Key: $apiKey, Model ID: $modelId")


        if (apiUrl.isNullOrEmpty() || apiKey.isNullOrEmpty()) {
            _resultText.value = context.getString(R.string.ocr_error_api_config_missing)
            return
        }

        // Update UI state to indicate processing
        _resultText.value = context.getString(R.string.ocr_ai_processing)
        // _selectedImageBitmap.value = null // Optional: Clear previous bitmap or show new one

        // Launch a coroutine for network operation
        // TODO: Consider injecting a CoroutineScope for better lifecycle management and testability
        coroutineScope.launch(Dispatchers.IO) {
            try {
                // 2. Convert Uri to Bitmap, then to Base64
                val bitmap = loadBitmapFromUri(imageUri) // Assuming loadBitmapFromUri is accessible and works
                withContext(Dispatchers.Main) {
                    _selectedImageBitmap.value = bitmap // Show the selected image while processing
                }
                val base64Image = bitmap.toBase64() // Using the extension function

                // 3. Create Retrofit instance
                val loggingInterceptor = HttpLoggingInterceptor().apply {
                    level = HttpLoggingInterceptor.Level.BODY // Or Level.BASIC for less verbose logs
                }
                val okHttpClient = OkHttpClient.Builder()
                    .addInterceptor(loggingInterceptor)
                    // Add other configurations like timeouts if needed
                    .build()

                val retrofit = Retrofit.Builder()
                    .baseUrl("http://placeholder.com/") // Placeholder, as @Url will override
                    .client(okHttpClient)
                    .addConverterFactory(GsonConverterFactory.create())
                    .build()

                val apiService = retrofit.create(AiOcrApiService::class.java)

                // 4. Make the network request
                val imageUrl = "data:image/jpeg;base64,$base64Image"
                val messages = listOf(
                    Message(
                        role = "user",
                        content = listOf(
                            ContentPart(type = "text", text = userPrompt),
                            ContentPart(type = "image_url", image_url = ImageUrl(url = imageUrl))
                        )
                    )
                )
                // Ensure modelId is not null; if it is, this indicates a problem with settings or default.
                // For now, let's assume modelId from SharedPreferences is valid and non-null.
                // If API requires a default or specific model for this endpoint, adjust accordingly.
                val currentModelId = modelId ?: "deepseek-ai/DeepSeek-V3" // Fallback, ensure this is the desired default if settings are missing
                Log.d("OcrHandler", "Using modelId: $currentModelId (from settings or fallback)")

                val request = AiOcrRequest(model = currentModelId, messages = messages)
                
                val response = apiService.recognizeImage(fullUrl = apiUrl, authorization = "Bearer $apiKey", request = request)

                withContext(Dispatchers.Main) {
                    val extractedText = response.choices?.firstOrNull()?.message?.content
                    val apiErrorMessage = response.error?.message // From a 200 OK response that contains an error object

                    if (extractedText != null) {
                        _resultText.value = extractedText.ifEmpty { context.getString(R.string.ocr_no_text_found_ai) }
                        Log.d("OcrHandler", "AI OCR successful: $extractedText")
                    } else if (apiErrorMessage != null) {
                        _resultText.value = context.getString(R.string.ocr_error_ai_failed, apiErrorMessage)
                        Log.e("OcrHandler", "AI OCR API error (in 200 OK response): $apiErrorMessage")
                    } else {
                        _resultText.value = context.getString(R.string.ocr_error_ai_failed, "Unknown response structure from API")
                        Log.w("OcrHandler", "AI OCR: Unknown response structure. Response: $response")
                    }
                }
            } catch (e: HttpException) {
                Log.e("OcrHandler", "AI OCR failed - HTTP Error ${e.code()}: ${e.message()}", e)
                var displayMessage = "HTTP ${e.code()}: ${e.message().take(100)}" // Truncate long generic messages
                try {
                    val errorBodyString = e.response()?.errorBody()?.string()
                    if (!errorBodyString.isNullOrEmpty()) {
                        Log.d("OcrHandler", "HTTP Error Body: $errorBodyString")
                        val gson = Gson()
                        val parsedAsOwnError = try { gson.fromJson(errorBodyString, AiOcrResponse::class.java) } catch (ex: Exception) { null }
                        if (parsedAsOwnError?.error?.message != null) {
                            displayMessage = parsedAsOwnError.error.message
                        } else {
                            // If not our specific error structure, or parsing failed,
                            // and if error body is reasonably short and not HTML, use it directly.
                            if (errorBodyString.length < 250 && !errorBodyString.contains("<html>", ignoreCase = true) && !errorBodyString.contains("<HTML>", ignoreCase = false)) {
                                displayMessage = errorBodyString
                            }
                        }
                    }
                } catch (ex: Exception) {
                    Log.w("OcrHandler", "Exception while parsing error body: ${ex.message}", ex)
                }
                withContext(Dispatchers.Main) {
                    _resultText.value = context.getString(R.string.ocr_error_ai_failed, displayMessage)
                }
            } catch (e: IOException) { // Catch Bitmap, IO, or other general errors before network
                Log.e("OcrHandler", "AI OCR failed - IO/Bitmap Error", e)
                withContext(Dispatchers.Main) {
                    _resultText.value = context.getString(R.string.ocr_error_ai_failed, e.localizedMessage ?: "Bitmap or IO error")
                }
            } catch (e: Exception) { // Catch other general exceptions
                Log.e("OcrHandler", "AI OCR failed - General Error", e)
                withContext(Dispatchers.Main) {
                    _resultText.value = context.getString(R.string.ocr_error_ai_failed, e.localizedMessage ?: "An unexpected error occurred")
                }
            }
        }
    }
}
