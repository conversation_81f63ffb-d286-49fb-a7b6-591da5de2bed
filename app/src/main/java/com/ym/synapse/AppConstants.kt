package com.ym.synapse

// SharedPreferences keys
const val PREFS_NAME = "ai_ocr_prefs"
const val KEY_API_URL = "api_url"
const val KEY_API_KEY = "api_key"
const val KEY_AI_OCR_MODEL_ID = "ai_ocr_model_id"
const val KEY_AI_OCR_PROMPT = "ai_ocr_prompt"
const val DEFAULT_AI_OCR_PROMPT = "Extract all text from this image."
const val DEFAULT_AI_OCR_MODEL_ID = ""

// 模型配置模式
const val KEY_MODEL_CONFIG_MODE = "model_config_mode"
const val MODEL_CONFIG_SHARED = "shared"
const val MODEL_CONFIG_SEPARATE = "separate"

// 图片类型定义
const val IMAGE_TYPE_TEXT_HEAVY = "Text-Heavy"
const val IMAGE_TYPE_RICH_CONTENT = "Rich-Content"
const val IMAGE_TYPE_SIMPLE_IMAGE = "Simple-Image"

// 各类型的配置键
// Text-Heavy 配置
const val KEY_TEXT_HEAVY_API_URL = "text_heavy_api_url"
const val KEY_TEXT_HEAVY_API_KEY = "text_heavy_api_key"
const val KEY_TEXT_HEAVY_MODEL_ID = "text_heavy_model_id"
const val KEY_TEXT_HEAVY_PROMPT = "text_heavy_prompt"

// Rich-Content 配置
const val KEY_RICH_CONTENT_API_URL = "rich_content_api_url"
const val KEY_RICH_CONTENT_API_KEY = "rich_content_api_key"
const val KEY_RICH_CONTENT_MODEL_ID = "rich_content_model_id"
const val KEY_RICH_CONTENT_PROMPT = "rich_content_prompt"

// Simple-Image 配置
const val KEY_SIMPLE_IMAGE_API_URL = "simple_image_api_url"
const val KEY_SIMPLE_IMAGE_API_KEY = "simple_image_api_key"
const val KEY_SIMPLE_IMAGE_MODEL_ID = "simple_image_model_id"
const val KEY_SIMPLE_IMAGE_PROMPT = "simple_image_prompt"

// 默认提示词
const val DEFAULT_TEXT_HEAVY_PROMPT = "请准确识别并提取这张文字密集图片中的所有文本内容。要求：\n\n1. **完整性**：提取所有可见的文字，不要遗漏任何内容\n2. **准确性**：保持原文的准确性，包括标点符号、数字、特殊字符\n3. **格式保持**：尽量保持原文的段落结构和换行\n4. **顺序正确**：按照阅读顺序（从上到下，从左到右）排列文本\n5. **语言识别**：自动识别中文、英文等不同语言\n\n如果图片包含代码，请保持代码的缩进和格式。\n如果图片包含表格，请用合适的方式表示表格结构。\n\n直接输出提取的文本内容，无需添加额外说明。"
const val DEFAULT_RICH_CONTENT_PROMPT = "分析这张富内容图片，请提供以下信息：\n1. 主要内容类型（如：社交媒体、新闻、购物、设置界面等）\n2. 关键信息摘要\n3. 重要的文字内容\n4. 图片中的数据或数字\n5. 用户界面元素的功能说明\n\n请用中文回答，结构清晰。"
const val DEFAULT_SIMPLE_IMAGE_PROMPT = "请简洁地描述这张图片的内容，包括：\n1. 主要对象或场景\n2. 颜色和构图特点\n3. 图片的整体风格或类型\n\n请用中文回答，保持简洁明了。"
