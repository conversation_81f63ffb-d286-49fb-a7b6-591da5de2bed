package com.ym.synapse

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.PowerSettingsNew
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.ym.synapse.ui.theme.SynapseTheme
import com.ym.synapse.utils.RootLspHelper
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TutorialScreen(
    onBackClick: () -> Unit = {},
    onComplete: () -> Unit = {}
) {
    val context = LocalContext.current
    var countdown by remember { mutableStateOf(20) }
    var showPermissionDialog by remember { mutableStateOf(false) }
    var isCountdownActive by remember { mutableStateOf(true) }
    var screenshotTriggered by remember { mutableStateOf(false) }
    var showCompleteScreen by remember { mutableStateOf(false) }
    
    // 倒计时逻辑
    LaunchedEffect(isCountdownActive) {
        if (isCountdownActive) {
            while (countdown > 0 && !screenshotTriggered) {
                delay(1000)
                countdown--
            }
            if (!screenshotTriggered) {
                // 倒计时结束，显示权限对话框
                showPermissionDialog = true
            }
            isCountdownActive = false
        }
    }

    // 监听截图触发
    LaunchedEffect(Unit) {
        val sharedPreferences = context.getSharedPreferences("app_state", Context.MODE_PRIVATE)
        while (isCountdownActive && !screenshotTriggered) {
            delay(500) // 每500ms检查一次
            val triggered = sharedPreferences.getBoolean("screenshot_triggered", false)
            if (triggered) {
                screenshotTriggered = true
                isCountdownActive = false
                // 清除标记
                with(sharedPreferences.edit()) {
                    remove("screenshot_triggered")
                    apply()
                }
                // 延迟2秒后显示完成页面
                delay(2000)
                showCompleteScreen = true
                break
            }
        }
    }

    // 显示完成页面
    if (showCompleteScreen) {
        ConfigCompleteScreen(onComplete = onComplete)
        return
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
            .padding(16.dp)
    ) {
        // 顶部栏
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
            }
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "使用教程",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 主要内容区域
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // 电源键图标
            Icon(
                imageVector = Icons.Default.PowerSettingsNew,
                contentDescription = null,
                modifier = Modifier.size(120.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 主要提示文字
            Text(
                text = "双击电源键截图",
                style = MaterialTheme.typography.headlineLarge,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "请快速双击电源键进行截图\n系统将自动识别并总结屏幕内容",
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 倒计时显示或成功状态
            if (isCountdownActive) {
                Card(
                    modifier = Modifier.padding(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.secondaryContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "等待截图触发",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSecondaryContainer
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "${countdown}秒",
                            style = MaterialTheme.typography.headlineMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onSecondaryContainer
                        )
                    }
                }
            } else if (screenshotTriggered) {
                Card(
                    modifier = Modifier.padding(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "✅ 截图触发成功！",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "双击电源键功能正常工作",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
            }
        }
        
        // 底部说明和完成按钮
        Column {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "💡 使用提示",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "• 确保已授予所有必要权限\n• 双击电源键的间隔要快\n• 首次使用可能需要额外权限确认",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 完成教程按钮
            Button(
                onClick = {
                    // 清除应用状态，进入正常使用模式
                    val sharedPreferences = context.getSharedPreferences("app_state", Context.MODE_PRIVATE)
                    with(sharedPreferences.edit()) {
                        putBoolean("show_api_config", false)
                        putBoolean("show_tutorial", false)
                        putBoolean("tutorial_completed", true)
                        apply()
                    }
                    onComplete()
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("完成教程，进入应用")
            }
        }
    }
    
    // 权限检查对话框
    if (showPermissionDialog) {
        AlertDialog(
            onDismissRequest = { showPermissionDialog = false },
            title = { 
                Text(
                    text = "权限检查",
                    fontWeight = FontWeight.Bold
                ) 
            },
            text = { 
                Text("未检测到截图触发，可能需要授予以下权限：\n\n• LSP模块权限\n• Root权限\n\n请确认是否已正确授予这些权限？") 
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showPermissionDialog = false
                        // 检查LSP和Root权限
                        val permissionStatus = RootLspHelper.checkAllPermissions(context)
                        android.util.Log.d("TutorialScreen", "Root: ${permissionStatus.hasRoot}")
                        android.util.Log.d("TutorialScreen", "LSP Framework: ${permissionStatus.hasLspFramework}")
                        android.util.Log.d("TutorialScreen", "LSP Module: ${permissionStatus.hasLspModule}")
                        android.util.Log.d("TutorialScreen", "Current App LSP: ${permissionStatus.hasCurrentAppLsp}")

                        // TODO: 显示权限检查结果界面
                    }
                ) {
                    Text("检查权限")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { 
                        showPermissionDialog = false
                        // 重新开始倒计时
                        countdown = 20
                        isCountdownActive = true
                        screenshotTriggered = false
                        showCompleteScreen = false
                    }
                ) {
                    Text("重试")
                }
            }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun TutorialScreenPreview() {
    SynapseTheme {
        TutorialScreen()
    }
}
