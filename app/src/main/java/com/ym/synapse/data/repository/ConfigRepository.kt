package com.ym.synapse.data.repository

import android.content.Context
import android.content.SharedPreferences
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 配置数据仓库
 * 统一管理应用的所有配置数据，提供响应式的配置更新
 */
class ConfigRepository private constructor(context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: ConfigRepository? = null
        
        fun getInstance(context: Context): ConfigRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ConfigRepository(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences("synapse_prefs", Context.MODE_PRIVATE)
    
    // 配置状态流
    private val _apiConfig = MutableStateFlow(getApiConfig())
    val apiConfig: Flow<ApiConfig> = _apiConfig.asStateFlow()
    
    private val _appState = MutableStateFlow(getAppState())
    val appState: Flow<AppState> = _appState.asStateFlow()
    
    /**
     * API配置数据类
     */
    data class ApiConfig(
        val apiUrl: String = "",
        val apiKey: String = "",
        val modelId: String = "gpt-4o-mini",
        val isConfigured: Boolean = false
    ) {
        fun isValid(): Boolean = apiUrl.isNotBlank() && apiKey.isNotBlank() && modelId.isNotBlank()
    }
    
    /**
     * 应用状态数据类
     */
    data class AppState(
        val showApiConfig: Boolean = false,
        val showTutorial: Boolean = false,
        val isFirstLaunch: Boolean = true,
        val hasCompletedSetup: Boolean = false
    )
    
    /**
     * 获取API配置
     */
    private fun getApiConfig(): ApiConfig {
        return ApiConfig(
            apiUrl = sharedPreferences.getString("api_url", "") ?: "",
            apiKey = sharedPreferences.getString("api_key", "") ?: "",
            modelId = sharedPreferences.getString("ai_ocr_model_id", "gpt-4o-mini") ?: "gpt-4o-mini",
            isConfigured = sharedPreferences.getBoolean("api_configured", false)
        )
    }
    
    /**
     * 获取应用状态
     */
    private fun getAppState(): AppState {
        return AppState(
            showApiConfig = sharedPreferences.getBoolean("show_api_config", false),
            showTutorial = sharedPreferences.getBoolean("show_tutorial", false),
            isFirstLaunch = sharedPreferences.getBoolean("is_first_launch", true),
            hasCompletedSetup = sharedPreferences.getBoolean("has_completed_setup", false)
        )
    }
    
    /**
     * 保存API配置
     */
    fun saveApiConfig(config: ApiConfig) {
        sharedPreferences.edit().apply {
            putString("api_url", config.apiUrl)
            putString("api_key", config.apiKey)
            putString("ai_ocr_model_id", config.modelId)
            putBoolean("api_configured", config.isValid())
            apply()
        }
        _apiConfig.value = config.copy(isConfigured = config.isValid())
    }
    
    /**
     * 保存应用状态
     */
    fun saveAppState(state: AppState) {
        sharedPreferences.edit().apply {
            putBoolean("show_api_config", state.showApiConfig)
            putBoolean("show_tutorial", state.showTutorial)
            putBoolean("is_first_launch", state.isFirstLaunch)
            putBoolean("has_completed_setup", state.hasCompletedSetup)
            apply()
        }
        _appState.value = state
    }
    
    /**
     * 更新API配置的单个字段
     */
    fun updateApiUrl(url: String) {
        val current = _apiConfig.value
        saveApiConfig(current.copy(apiUrl = url))
    }
    
    fun updateApiKey(key: String) {
        val current = _apiConfig.value
        saveApiConfig(current.copy(apiKey = key))
    }
    
    fun updateModelId(modelId: String) {
        val current = _apiConfig.value
        saveApiConfig(current.copy(modelId = modelId))
    }
    
    /**
     * 更新应用状态的单个字段
     */
    fun updateShowApiConfig(show: Boolean) {
        val current = _appState.value
        saveAppState(current.copy(showApiConfig = show))
    }
    
    fun updateShowTutorial(show: Boolean) {
        val current = _appState.value
        saveAppState(current.copy(showTutorial = show))
    }
    
    fun markSetupCompleted() {
        val current = _appState.value
        saveAppState(current.copy(
            hasCompletedSetup = true,
            isFirstLaunch = false,
            showApiConfig = false,
            showTutorial = false
        ))
    }
    
    /**
     * 获取模型配置
     * TODO: 实现模型配置管理
     */
    fun getModelConfig(imageType: String): String? {
        return sharedPreferences.getString("model_config_$imageType", null)
    }

    /**
     * 保存模型配置
     * TODO: 实现模型配置管理
     */
    fun saveModelConfig(imageType: String, config: String) {
        sharedPreferences.edit().putString("model_config_$imageType", config).apply()
    }
    
    /**
     * 重置所有配置
     */
    fun resetAllConfigs() {
        sharedPreferences.edit().clear().apply()
        _apiConfig.value = ApiConfig()
        _appState.value = AppState()
    }
    
    /**
     * 检查是否需要显示初始设置
     */
    fun shouldShowInitialSetup(): Boolean {
        val apiConfig = _apiConfig.value
        val appState = _appState.value
        
        return appState.isFirstLaunch || !apiConfig.isConfigured || !appState.hasCompletedSetup
    }
    
    /**
     * 获取当前配置摘要
     */
    fun getConfigSummary(): String {
        val apiConfig = _apiConfig.value
        val appState = _appState.value
        
        return buildString {
            appendLine("=== 配置摘要 ===")
            appendLine("API URL: ${if (apiConfig.apiUrl.isNotBlank()) "已配置" else "未配置"}")
            appendLine("API Key: ${if (apiConfig.apiKey.isNotBlank()) "已配置" else "未配置"}")
            appendLine("模型ID: ${apiConfig.modelId}")
            appendLine("配置完成: ${apiConfig.isConfigured}")
            appendLine("设置完成: ${appState.hasCompletedSetup}")
            appendLine("首次启动: ${appState.isFirstLaunch}")
        }
    }
}
